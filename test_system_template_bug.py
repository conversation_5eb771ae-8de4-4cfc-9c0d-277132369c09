#!/usr/bin/env python3
"""
测试系统模板ID格式bug的专门测试脚本
"""

import os
import sys
import django
import requests
import json

# 设置Django环境
sys.path.append('/home/<USER>/Desktop/program/project/<PERSON>anzi')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Tuanzi_Backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from core.models import SystemTemplate, TemplateManager
from events.models import EventTemplate

User = get_user_model()

def test_template_id_bug():
    """测试系统模板ID格式bug"""
    print("🧪 测试系统模板ID格式bug...")
    
    BASE_URL = "http://127.0.0.1:8000/api"
    
    # 1. 创建测试用户
    print("1. 创建测试用户...")
    try:
        user = User.objects.get(username='test')
        print(f"   ✅ 用户已存在: {user.username}")
    except User.DoesNotExist:
        user = User.objects.create_user(
            username='test',
            password='testpass123',
            subscription_level='FREE'
        )
        print(f"   ✅ 用户创建成功: {user.username}")

    # 确保密码正确
    user.set_password('testpass123')
    user.save()
    
    # 2. 登录获取token
    print("2. 登录获取token...")
    login_data = {
        'username': 'test',
        'password': 'testpass123'
    }
    response = requests.post(f"{BASE_URL}/token/", json=login_data)
    if response.status_code == 200:
        token_data = response.json()
        access_token = token_data['access']
        headers = {'Authorization': f'Bearer {access_token}'}
        print(f"   ✅ 登录成功")
    else:
        print(f"   ❌ 登录失败: {response.text}")
        return False
    
    # 3. 获取房间模板列表
    print("3. 获取房间模板列表...")
    response = requests.get(f"{BASE_URL}/room-templates/", headers=headers)
    if response.status_code == 200:
        templates_data = response.json()
        templates = templates_data.get('templates', [])
        print(f"   ✅ 获取到 {len(templates)} 个模板")
        
        # 查找系统模板
        system_templates = [t for t in templates if t['type'] == 'system']
        if system_templates:
            system_template = system_templates[0]
            template_id = system_template['id']
            print(f"   📋 系统模板ID: {template_id}")
            print(f"   📋 系统模板名称: {system_template['name']}")
        else:
            print("   ❌ 没有找到系统模板")
            return False
    else:
        print(f"   ❌ 获取模板列表失败: {response.text}")
        return False
    
    # 4. 尝试使用系统模板创建房间
    print("4. 尝试使用系统模板创建房间...")
    room_data = {'template_id': template_id}
    response = requests.post(f"{BASE_URL}/rooms/create/", json=room_data, headers=headers)
    
    print(f"   状态码: {response.status_code}")
    print(f"   响应内容: {response.text}")
    
    if response.status_code == 201:
        room_info = response.json()
        room_code = room_info.get('room_code')
        print(f"   ✅ 房间创建成功: {room_code}")
        return True
    else:
        print(f"   ❌ 房间创建失败")
        print(f"   错误详情: {response.text}")
        return False

def test_template_manager_directly():
    """直接测试TemplateManager"""
    print("\n🧪 直接测试TemplateManager...")
    
    # 获取测试用户
    user = User.objects.get(username='test')
    
    # 获取可用模板
    templates = TemplateManager.get_available_templates_for_user(user)
    print(f"   📋 用户可用模板数量: {len(templates)}")
    
    for template in templates:
        print(f"   - {template['id']}: {template['name']} ({template['type']})")
        
        # 测试get_template_by_id
        template_obj, template_type = TemplateManager.get_template_by_id(template['id'], user)
        if template_obj:
            print(f"     ✅ 可以通过ID获取: {template_obj.name}")
        else:
            print(f"     ❌ 无法通过ID获取")

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 系统模板ID格式bug测试")
    print("=" * 60)
    
    # 测试API
    api_success = test_template_id_bug()
    
    # 测试TemplateManager
    test_template_manager_directly()
    
    print("\n" + "=" * 60)
    if api_success:
        print("✅ 测试通过：系统模板ID格式正常工作")
    else:
        print("❌ 测试失败：发现系统模板ID格式bug")
    print("=" * 60)
